# version: 0.2

# phases:
#   pre_build:
#     commands:
#       - echo "Launching EC2 instance to run tests"
#       - |
#         INSTANCE_ID=$(aws ec2 run-instances \
#           --image-id ami-0e6cc441f9f4caab3 \
#           --count 1 \
#           --instance-type g5.2xlarge \
#           --key-name production/ssh/isaaclab \
#           --security-group-ids sg-02617e4b8916794c4 \
#           --subnet-id subnet-0907ceaeb40fd9eac \
#           --block-device-mappings '[{"DeviceName":"/dev/sda1","Ebs":{"VolumeSize":500}}]' \
#           --output text \
#           --query 'Instances[0].InstanceId')
#       - aws ec2 wait instance-running --instance-ids $INSTANCE_ID
#       - |
#         EC2_INSTANCE_IP=$(aws ec2 describe-instances \
#           --filters "Name=instance-state-name,Values=running" "Name=instance-id,Values=$INSTANCE_ID" \
#           --query 'Reservations[*].Instances[*].[PrivateIpAddress]' \
#           --output text)
#       - mkdir -p ~/.ssh
#       - |
#         aws ec2 describe-key-pairs --include-public-key --key-name production/ssh/isaaclab \
#           --query 'KeyPairs[0].PublicKey' --output text > ~/.ssh/id_rsa.pub
#       - |
#         aws secretsmanager get-secret-value --secret-id production/ssh/isaaclab \
#           --query SecretString --output text > ~/.ssh/id_rsa
#       - chmod 400 ~/.ssh/id_*
#       - echo "Host $EC2_INSTANCE_IP\n\tStrictHostKeyChecking no\n" >> ~/.ssh/config
#       - |
#         aws ec2-instance-connect send-ssh-public-key \
#           --instance-id $INSTANCE_ID \
#           --availability-zone us-west-2a \
#           --ssh-public-key file://~/.ssh/id_rsa.pub \
#           --instance-os-user ubuntu
#   build:
#     commands:
#       - echo "Running tests on EC2 instance"
#       - SRC_DIR=$(basename $CODEBUILD_SRC_DIR)
#       - cd ..
#       - |
#         bash -c '
#         function retry_scp() {
#           local retries=5
#           local wait_time=30
#           local count=0
#           while [ $count -lt $retries ]; do
#             sleep $wait_time
#             scp -r $SRC_DIR ubuntu@$EC2_INSTANCE_IP:~
#             if [ $? -eq 0 ]; then
#               echo "SCP command succeeded"
#               return 0
#             fi
#             count=$((count + 1))
#             echo "SCP command failed. Retrying in $wait_time seconds..."
#           done
#           echo "SCP command failed after $retries attempts."
#           return 1
#         }
#         retry_scp
#         '
#       - ssh ubuntu@$EC2_INSTANCE_IP "docker login -u \\\$oauthtoken -p $NGC_TOKEN nvcr.io"
#       - |
#         ssh ubuntu@$EC2_INSTANCE_IP "
#           cd $SRC_DIR
#           DOCKER_BUILDKIT=1 docker build -t isaac-lab-dev \
#             --build-arg ISAACSIM_BASE_IMAGE_ARG=$ISAACSIM_BASE_IMAGE \
#             --build-arg ISAACSIM_VERSION_ARG=$ISAACSIM_BASE_VERSION \
#             --build-arg ISAACSIM_ROOT_PATH_ARG=/isaac-sim \
#             --build-arg ISAACLAB_PATH_ARG=/workspace/isaaclab \
#             --build-arg DOCKER_USER_HOME_ARG=/root \
#             -f docker/Dockerfile.base .
#           docker run --rm --entrypoint bash --gpus all --network=host \
#             --name isaac-lab-test isaac-lab-dev ./isaaclab.sh -t && exit \$?
#         "

# #   post_build:
# #     commands:
# #       - echo "Terminating EC2 instance"
# #       - aws ec2 terminate-instances --instance-ids $INSTANCE_ID
