Project Workflow
================

The following guidelines are recommendations for the workflow when developing with IsaacLab.
It is useful for both individual and team projects.

Start of a Project
~~~~~~~~~~~~~~~~~~

1. **Clone the RSL Fork of Isaac Lab**  The `private fork <https://github.com/leggedrobotics/IsaacLab-Internal>`_
   contains internal code and propriotary features. Then install the private fork into your conda environment.

   .. code:: bash

      <NAME_EMAIL>:leggedrobotics/IsaacLab-Internal.git
      cd IsaacLab-Internal
      python ./isaaclab.sh -i

   You can find all the changes compared to the public repo in the RSL Changelog.

2. **Build IsaacLab Docker** following the `Docker Instructions <https://leggedrobotics.github.io/IsaacLab-Internal/source/deployment/docker.html>`_
   which will contain your installation of Isaac<PERSON><PERSON>. A native install of <PERSON><PERSON><PERSON> is not possible, as students do not
   have sudo rights on the student workstations. In short,

   a. Setup the `NGC Key <https://leggedrobotics.github.io/IsaacLab-Internal/source/deployment/docker.html#obtaining-the-isaac-sim-container>`_.

   b. Run the following commands to build and enter the docker container

      .. code:: bash

         ./docker/container.py start
         ./docker/container.py enter

   .. note::

      Make sure to enable X-forwarding when asked for it during the docker build to make sure that the IsaacSim Gui
      can be streamed.

3. **Create own dev branch** called ``dev/<your_name>/<project_title>``. This branch is used to make
   changes to the core code (if necessary). For instance, if you need to add a new feature to the core code,
   you can do it in this branch. The new branch should not be a place to add new features to your specific project but
   only changes that can be merged back to main. Your work lives in an own extension (see next point).

   .. code:: bash

      git checkout -b dev/<your_name>/<project_title>

4. **Create repo for your own work:** IsaacLab includes an extension template which should be used to start your own
   project. Follow the instruction in the `extension template repository <https://github.com/isaac-sim/IsaacLabExtensionTemplate>`_.
   Importantly, click on the button "Use this template" to create a new repo under the **leggedrobotics** github
   organization, do not use your private account! If you cannot add a repo to the organization, speak with your
   supervisors. Also do not fork the template as your new repo will then be public!

Closing a Project
~~~~~~~~~~~~~~~~~

When you are done with your project, follow these steps:

1. **Resolve your dev branch**. Ensure that all the changes in your dev branch are either merged back to the main
   repository or in the extension template repository.
2. **Resolve open PRs**. Make sure that all open PRs are either merged or closed.

.. warning::

   Stale branches in Isaac Lab will be deleted after two months, so resolve it early! This is currently enforced by
   a GitHub action and cannot be revoked.

Best practices
~~~~~~~~~~~~~~

During the development of your project, we recommend the following best practices:

1. **Regularly synchronize with the Isaac Lab repository** to ensure that your project remains up-to-date with the
   latest features and improvements.

   - Regularly pull and merge the latest changes from the ``main-rsl`` branch into your dev branch.
   - We recommend doing this at least once a week.

2. **Send PRs to the Isaac Lab repository** if you develop something that is useful for other people.

   - Make sure that the PR is small and focused on a single feature. This helps to make the review process easier.
   - Make sure that the PR is decently documented and tested.
   - Make the PRs as frequent as possible. Don't wait for long until you make the PR (often it will only take 10min).
   - Follow object-oriented programming rules (when it doesn't add complexity to the code).

Please check the `Contributing Guide <https://isaac-sim.github.io/IsaacLab/source/refs/contributing.html>`_ for more
information.
