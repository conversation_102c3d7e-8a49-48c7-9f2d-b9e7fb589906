Releasing a Project
===================

When publishing your project (as an academic work or open-source contribution), consider the following guidelines:

1. **Contact <PERSON>:** <PERSON> has to give his okay if you want to open-source the project. Think precisely
   why you want to open-source it and what impact it might have. You cannot open-source any code without his approval.

2. **Format According to ETH Standard:** Any open-sourced code developed by ETH employees must be formatted according
   to ETH guidelines. The specifics can be found
   [here](https://ethz.ch/en/industry/researchers/licensing-software/open-source-software.html), generally the
   following points are important:

   - Clearly mark every piece of code that you took from another work
   - Archive your code at the ETH library before releasing, details [here](https://go.data-archive.ethz.ch/)
   - Add a license to your project and include a header in every file, e.g. for a BSD-3 license a possible header is

     .. code:: python

      # Copyright (c) 2023-2024, ETH Zurich (Robotics Systems Lab)
      # Author: <PERSON>
      # All rights reserved.
      #
      # SPDX-License-Identifier: BSD-3-Clause

     Hint: You can add a license automatically to all the files inside your repo by modifying
     `.gitub/LICENSE_HEADER.txt` and running the pre-commit formatting

     .. code:: bash

      pre-commit run --all-files

3. **Open-source your work:** The extension allows you to easily publish your work. Generally, it is recommended to
   use the ``leggedrobotics`` github to increase publicity. Please consider the following aspects.

   - When open-sourcing your work, it is important to segregate the features that are specific to your project and the
     features that can be useful for others. The former should be kept in your project repository, while the latter should
     be made public in the Isaac Lab repository.
   - If there are any features that are especially useful for RSL but not for the public, please make a PR to the
     RSL fork.

4. **Requiring internal features for open-sourcing:** In case you use features from Isaac Lab that are not public, consider
   the following:

   - Speak with the person who implemented the feature (if known) and ask if they are willing to make it public.
   - Check with the admins if the feature can be made public.
   - If the feature is not public and above steps fail, consider implementing the feature yourself or finding
     an alternative.

5. **Acknowledgments:** If you use features from the private repository, consider adding those who implemented
   the feature to the acknowledgments. This will help give credit to the people who made your project possible.

   .. note::

      For information on features internal to the organization, please check the changelog of the internal
      Isaac Lab repository.
