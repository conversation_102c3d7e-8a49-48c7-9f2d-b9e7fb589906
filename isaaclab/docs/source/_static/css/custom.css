/*
 * For reference: https://pydata-sphinx-theme.readthedocs.io/en/v0.9.0/user_guide/customizing.html
 * For colors: https://clrs.cc/
 */

/* anything related to the light theme */
html[data-theme="light"] {
    --pst-color-primary: #0c7dc3;
    --pst-color-secondary: #045486;
    --pst-color-secondary-highlight: #045486;
    --pst-color-inline-code-links: #0c7dc3;
    --pst-color-info: var(--pst-color-primary);
    --pst-color-info-highlight: var(--pst-color-primary);
    --pst-color-info-bg: #c1e7ff;
    --pst-color-attention: #ffc107;
    --pst-color-text-base: #323232;
    --pst-color-text-muted: #646464;
    --pst-color-shadow: #d8d8d8;
    --pst-color-border: #c9c9c9;
    --pst-color-inline-code: #0c7dc3;
    --pst-color-target: #fbe54e;
    --pst-color-background: #fff;
    --pst-color-on-background: #fff;
    --pst-color-surface: #f5f5f5;
    --pst-color-on-surface: #e1e1e1;
    --pst-color-link: var(--pst-color-primary);
    --pst-color-link-hover: #479bd0;
    --pst-color-table-row-hover-bg: #d7edf7;
    --pst-color-accent: var(--pst-color-primary);
}

/* anything related to the dark theme */
html[data-theme="dark"] {
    --pst-color-primary: #75b8e2;
    --pst-color-secondary: #75b8e2;
    --pst-color-secondary-highlight: #75b8e2;
    --pst-color-inline-code-links: #75b8e2;
    --pst-color-info: var(--pst-color-primary);
    --pst-color-info-highlight: var(--pst-color-primary);
    --pst-color-info-bg: #07476f;
    --pst-color-attention: #dca90f;
    --pst-color-text-base: #cecece;
    --pst-color-text-muted: #a6a6a6;
    --pst-color-shadow: #212121;
    --pst-color-border: silver;
    --pst-color-inline-code: #0c7dc3;
    --pst-color-target: #472700;
    --pst-color-background: #121212;
    --pst-color-on-background: #1e1e1e;
    --pst-color-surface: #212121;
    --pst-color-on-surface: #373737;
    --pst-color-link: var(--pst-color-primary);
    --pst-color-link-hover: #75b8e2;
    --pst-color-table-row-hover-bg: #2e6f97;
    --pst-color-accent: var(--pst-color-primary);
}

a {
    text-decoration: none !important;
}

/* for the announcement link */
.bd-header-announcement a,
.bd-header-version-warning a {
    color: #7FDBFF;
}

/* for the search box in the navbar */
.form-control {
    border-radius: 0 !important;
    border: none !important;
    outline: none !important;
}

/* reduce padding for logo */
.navbar-brand {
    padding-top: 0.0rem !important;
    padding-bottom: 0.0rem !important;
}

.navbar-icon-links {
    padding-top: 0.0rem !important;
    padding-bottom: 0.0rem !important;
}

/* showcase task tables */

.showcase-table {
    min-width: 75%;
}

.showcase-table td {
    border-color: gray;
    border-style: solid;
    border-width: 1px;
}

.showcase-table p {
    margin: 0;
    padding: 0;
}

.showcase-table .rot90 {
    transform: rotate(-90deg);
    margin: 0;
    padding: 0;
}

.showcase-table .center {
    text-align: center;
    vertical-align: middle;
}
