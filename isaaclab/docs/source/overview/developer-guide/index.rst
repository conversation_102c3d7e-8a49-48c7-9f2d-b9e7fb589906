Developer's Guide
=================

For development, we suggest using `Microsoft Visual Studio Code
(VSCode) <https://code.visualstudio.com/>`__. This is also suggested by
NVIDIA Omniverse and there exists tutorials on how to `debug Omniverse
extensions <https://www.youtube.com/watch?v=Vr1bLtF1f4U&ab_channel=NVIDIAOmniverse>`__
using VSCode.

.. toctree::
  :maxdepth: 1

  VS Code <vs_code>
  repo_structure
  development
  template
