.. _setup-vs-code:

Setting up Visual Studio Code
-----------------------------

**This is optional.  You do not need to use VScode to use Isaac Lab**

`Visual Studio Code <https://code.visualstudio.com/>`_ has proven an invaluable tool for the development of Isaac Lab. The Isaac Lab repository includes the VSCode files for setting up your development environment. These are included in the ``.vscode`` directory and include the following files:

.. code-block:: bash

   .vscode
   ├── tools
   │   ├── launch.template.json
   │   ├── settings.template.json
   │   └── setup_vscode.py
   ├── extensions.json
   ├── launch.json  # <- this is generated by setup_vscode.py
   ├── settings.json  # <- this is generated by setup_vscode.py
   └── tasks.json


.. attention::

   The following instructions on setting up Visual Studio Code only work with
   :ref:`Isaac Sim Binaries Installation <isaaclab-binaries-installation>` and not with
   :ref:`Pip Installation <isaaclab-pip-installation>`.


To setup the IDE, please follow these instructions:

1. Open the ``IsaacLab`` directory on Visual Studio Code IDE
2. Run VSCode `Tasks <https://code.visualstudio.com/docs/editor/tasks>`__, by
   pressing ``Ctrl+Shift+P``, selecting ``Tasks: Run Task`` and running the
   ``setup_python_env`` in the drop down menu.

   .. image:: ../../_static/vscode_tasks.png
      :width: 600px
      :align: center
      :alt: VSCode Tasks


.. note::
   If this is your first time running tasks in VS Code, you may be prompted to select how to handle warnings. Simply follow
   the prompts until the task window closes.

If everything executes correctly, it should create the following files:

* ``.vscode/launch.json``: Contains the launch configurations for debugging python code.
* ``.vscode/settings.json``: Contains the settings for the python interpreter and the python environment.

For more information on VSCode support for Omniverse, please refer to the
following links:

* `Isaac Sim VSCode support <https://docs.omniverse.nvidia.com/app_isaacsim/app_isaacsim/manual_standalone_python.html#isaac-sim-python-vscode>`__
* `Debugging with VSCode <https://docs.isaacsim.omniverse.nvidia.com/latest/development_tools/vscode.html#visual-studio-code-vs-code>`__


Configuring the python interpreter
----------------------------------

In the provided configuration, we set the default python interpreter to use the
python executable provided by Omniverse. This is specified in the
``.vscode/settings.json`` file:

.. code-block:: json

   {
      "python.defaultInterpreterPath": "${workspaceFolder}/_isaac_sim/python.sh",
   }

If you want to use a different python interpreter (for instance, from your conda environment),
you need to change the python interpreter used by selecting and activating the python interpreter
of your choice in the bottom left corner of VSCode, or opening the command palette (``Ctrl+Shift+P``)
and selecting ``Python: Select Interpreter``.

For more information on how to set python interpreter for VSCode, please
refer to the `VSCode documentation <https://code.visualstudio.com/docs/python/environments#_working-with-python-interpreters>`_.
