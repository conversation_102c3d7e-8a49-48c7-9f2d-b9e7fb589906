﻿isaaclab.actuators
==================

.. automodule:: isaaclab.actuators

  .. rubric:: Classes

  .. autosummary::

    ActuatorBase
    ActuatorBaseCfg
    ImplicitActuator
    ImplicitActuatorCfg
    IdealPDActuator
    IdealPDActuatorCfg
    DCMotor
    DCMotorCfg
    DelayedPDActuator
    DelayedPDActuatorCfg
    RemotizedPDActuator
    RemotizedPDActuatorCfg
    ParallelDynaarmDCMotor
    ParallelDynaarmDCMotorCfg
    ActuatorNetMLP
    ActuatorNetMLPCfg
    ActuatorNetLSTM
    ActuatorNetLSTMCfg

Actuator Base
-------------

.. autoclass:: ActuatorBase
  :members:
  :inherited-members:

.. autoclass:: ActuatorBaseCfg
  :members:
  :inherited-members:
  :exclude-members: __init__, class_type

Implicit Actuator
-----------------

.. autoclass:: ImplicitActuator
  :members:
  :inherited-members:
  :show-inheritance:

.. autoclass:: ImplicitActuatorCfg
  :members:
  :inherited-members:
  :show-inheritance:
  :exclude-members: __init__, class_type

Ideal PD Actuator
-----------------

.. autoclass:: IdealPDActuator
  :members:
  :inherited-members:
  :show-inheritance:

.. autoclass:: IdealPDActuatorCfg
  :members:
  :inherited-members:
  :show-inheritance:
  :exclude-members: __init__, class_type

DC Motor Actuator
-----------------

.. autoclass:: DCMotor
  :members:
  :inherited-members:
  :show-inheritance:

.. autoclass:: DCMotorCfg
  :members:
  :inherited-members:
  :show-inheritance:
  :exclude-members: __init__, class_type

Delayed PD Actuator
-------------------

.. autoclass:: DelayedPDActuator
  :members:
  :inherited-members:
  :show-inheritance:

.. autoclass:: DelayedPDActuatorCfg
  :members:
  :inherited-members:
  :show-inheritance:
  :exclude-members: __init__, class_type

Remotized PD Actuator
---------------------

.. autoclass:: RemotizedPDActuator
  :members:
  :inherited-members:
  :show-inheritance:

.. autoclass:: RemotizedPDActuatorCfg
  :members:
  :inherited-members:
  :show-inheritance:
  :exclude-members: __init__, class_type

Parallel Dynaarm DC Motor Actuator
----------------------------------

.. autoclass:: ParallelDynaarmDCMotor
  :members:
  :inherited-members:
  :show-inheritance:

.. autoclass:: ParallelDynaarmDCMotorCfg
  :members:
  :inherited-members:
  :show-inheritance:
  :exclude-members: __init__, class_type

MLP Network Actuator
---------------------

.. autoclass:: ActuatorNetMLP
  :members:
  :inherited-members:
  :show-inheritance:

.. autoclass:: ActuatorNetMLPCfg
  :members:
  :inherited-members:
  :show-inheritance:
  :exclude-members: __init__, class_type

LSTM Network Actuator
---------------------

.. autoclass:: ActuatorNetLSTM
  :members:
  :inherited-members:
  :show-inheritance:

.. autoclass:: ActuatorNetLSTMCfg
  :members:
  :inherited-members:
  :show-inheritance:
  :exclude-members: __init__, class_type
