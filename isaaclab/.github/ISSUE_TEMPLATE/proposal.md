---
name: Proposal
about: Propose changes that are not bug fixes
title: "[Proposal] Proposal title"
---


### Proposal

A clear and concise description of the proposal. In a few sentences, describe the feature and its core capabilities.

### Motivation

Please outline the motivation for the proposal. Summarize the core use cases and user problems and needs you are trying to solve.

Is your feature request related to a problem? e.g.,"I'm always frustrated when [...]".

If this is related to another GitHub issue, please link here too.

### Alternatives

A clear and concise description of any alternative solutions or features you've considered, if any.

### Additional context

Add any other context or screenshots about the feature request here.

### Checklist

- [ ] I have checked that there is no similar issue in the repo (**required**)

### Acceptance Criteria

Add the criteria for which this task is considered **done**. If not known at issue creation time, you can add this once the issue is assigned.

- [ ] Criteria 1
- [ ] Criteria 2
