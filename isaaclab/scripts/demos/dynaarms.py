# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This script demonstrates dynaarm manipulators.

.. code-block:: bash

    # Usage
    ./isaaclab.sh -p scripts/demos/dynaarms.py

"""

"""Launch Isaac Sim Simulator first."""

import argparse

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="This script demonstrates different dynaarm assets.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import numpy as np
import torch

import isaacsim.core.utils.prims as prim_utils

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation
from isaaclab.markers.config import FRAME_MARKER_CFG
from isaaclab.sensors import FrameTransformer, FrameTransformerCfg
from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR

##
# Pre-defined configs
##
# isort: off
from isaaclab_assets import DYNAARM_NO_TOOL_CFG, DYNAARM_WITH_COVERS_NO_TOOL_CFG

# isort: on


def define_origins(num_origins: int, spacing: float) -> list[list[float]]:
    """Defines the origins of the the scene."""
    # create tensor based on number of environments
    env_origins = torch.zeros(num_origins, 3)
    # create a grid of origins
    num_rows = np.floor(np.sqrt(num_origins))
    num_cols = np.ceil(num_origins / num_rows)
    xx, yy = torch.meshgrid(torch.arange(num_rows), torch.arange(num_cols), indexing="xy")
    env_origins[:, 0] = spacing * xx.flatten()[:num_origins] - spacing * (num_rows - 1) / 2
    env_origins[:, 1] = spacing * yy.flatten()[:num_origins] - spacing * (num_cols - 1) / 2
    env_origins[:, 2] = 0.0
    # return the origins
    return env_origins.tolist()


def design_scene() -> tuple[dict, list[list[float]]]:
    """Designs the scene."""

    sm_marker_cfg = FRAME_MARKER_CFG.copy()
    sm_marker_cfg.markers["frame"].scale = (0.08, 0.08, 0.08)

    # Ground-plane
    cfg = sim_utils.GroundPlaneCfg()
    cfg.func("/World/defaultGroundPlane", cfg)
    # Lights
    cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
    cfg.func("/World/Light", cfg)

    # Create separate groups called "Origin1", "Origin2", "Origin3"
    # Each group will have a mount and a robot on top of it
    origins = define_origins(num_origins=2, spacing=2.0)

    # Origin 1 with Franka Panda
    prim_utils.create_prim("/World/env_0", "Xform", translation=origins[0])
    # -- Table
    cfg = sim_utils.UsdFileCfg(usd_path=f"{ISAAC_NUCLEUS_DIR}/Props/Mounts/SeattleLabTable/table_instanceable.usd")
    cfg.func("/World/env_0/Table", cfg, translation=(0.55, 0.0, 1.05))
    # -- Robot
    dynaarm_cfg = DYNAARM_NO_TOOL_CFG.replace(prim_path="/World/env_0/Robot")
    dynaarm_cfg.init_state.pos = (0.0, 0.0, 1.05)
    dynaarm = Articulation(cfg=dynaarm_cfg)

    # Origin 2 with dynaarm with white covers
    prim_utils.create_prim("/World/env_1", "Xform", translation=origins[1])
    # -- Table
    cfg = sim_utils.UsdFileCfg(usd_path=f"{ISAAC_NUCLEUS_DIR}/Props/Mounts/SeattleLabTable/table_instanceable.usd")
    cfg.func("/World/env_1/Table", cfg, translation=(0.55, 0.0, 1.03))
    # -- Robot
    dynaarm_covers_cfg = DYNAARM_WITH_COVERS_NO_TOOL_CFG.replace(prim_path="/World/env_1/Robot")
    dynaarm_covers_cfg.init_state.pos = (0.0, 0.0, 1.03)
    dynaarm_covers = Articulation(cfg=dynaarm_covers_cfg)
    # -- Frame Transformer

    # Example using .* to get full body + LF_FOOT
    frame_transformer_cfg = FrameTransformerCfg(
        prim_path="/World/env_.*/Robot/dynaarm_base",
        target_frames=[
            FrameTransformerCfg.FrameCfg(prim_path="/World/env_.*/Robot/dynaarm_SHOULDER"),
            FrameTransformerCfg.FrameCfg(prim_path="/World/env_.*/Robot/dynaarm_UPPERARM"),
            FrameTransformerCfg.FrameCfg(prim_path="/World/env_.*/Robot/dynaarm_ELBOW"),
            FrameTransformerCfg.FrameCfg(prim_path="/World/env_.*/Robot/dynaarm_FOREARM"),
            FrameTransformerCfg.FrameCfg(prim_path="/World/env_.*/Robot/dynaarm_WRIST_1"),
            FrameTransformerCfg.FrameCfg(prim_path="/World/env_.*/Robot/dynaarm_WRIST_2"),
            FrameTransformerCfg.FrameCfg(
                prim_path="/World/env_.*/Robot/dynaarm_END_EFFECTOR",
                name="end_effector",
            ),
        ],
        debug_vis=True,
        visualizer_cfg=sm_marker_cfg.replace(prim_path="/Visuals/FrameTransformer"),
    )
    frame_transformer = FrameTransformer(frame_transformer_cfg)

    # return the scene information
    scene_entities = {
        "dynaarm": dynaarm,
        "dynaarm_covers": dynaarm_covers,
    }
    sensors = {
        "frame_transformer": frame_transformer,
    }
    return scene_entities, origins, sensors


def run_simulator(
    sim: sim_utils.SimulationContext,
    entities: dict[str, Articulation],
    origins: torch.Tensor,
    sensors: dict[str, FrameTransformer],
):
    """Runs the simulation loop."""
    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    sim_time = 0.0
    count = 0

    # Simulate physics
    while simulation_app.is_running():
        # reset
        if count % 200 == 0:
            # reset counters
            sim_time = 0.0
            count = 0
            # reset the scene entities
            for index, robot in enumerate(entities.values()):
                # root state
                root_state = robot.data.default_root_state.clone()
                root_state[:, :3] += origins[index]
                robot.write_root_state_to_sim(root_state)
                # set joint positions
                joint_pos, joint_vel = robot.data.default_joint_pos.clone(), robot.data.default_joint_vel.clone()
                robot.write_joint_state_to_sim(joint_pos, joint_vel)
                # clear internal buffers
                robot.reset()
            print("[INFO]: Resetting robots state...")

        # apply random actions to the robots
        for robot in entities.values():
            # generate random joint positions
            joint_pos_target = robot.data.default_joint_pos + torch.randn_like(robot.data.joint_pos) * 0.1
            joint_pos_target = joint_pos_target.clamp_(
                robot.data.soft_joint_pos_limits[..., 0], robot.data.soft_joint_pos_limits[..., 1]
            )
            # apply action to the robot
            robot.set_joint_position_target(joint_pos_target)
            # write data to sim
            robot.write_data_to_sim()

        # perform step
        sim.step()
        # update sim-time
        sim_time += sim_dt
        count += 1
        # update buffers
        for robot in entities.values():
            robot.update(sim_dt)

        for sensor in sensors.values():
            sensor.update(dt=sim_dt)


def main():
    """Main function."""
    # Initialize the simulation context
    sim_cfg = sim_utils.SimulationCfg()
    sim = sim_utils.SimulationContext(sim_cfg)
    # Set main camera
    sim.set_camera_view([3.5, 0.0, 3.2], [0.0, 0.0, 0.5])
    # design scene
    scene_entities, scene_origins, sensors = design_scene()
    scene_origins = torch.tensor(scene_origins, device=sim.device)
    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")
    # Run the simulator
    run_simulator(sim, scene_entities, scene_origins, sensors)


if __name__ == "__main__":
    # run the main function
    main()
    # close sim app
    simulation_app.close()
