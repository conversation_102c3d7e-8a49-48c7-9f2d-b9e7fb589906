# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This script demonstrates different endeffectors integrated with dynaarm.

.. code-block:: bash

    # Usage
    ./isaaclab.sh -p scripts/demos/end_effectors.py

"""

"""Launch Isaac Sim Simulator first."""

import argparse

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="This script demonstrates different end effectors.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import numpy as np
import torch

import isaacsim.core.utils.prims as prim_utils

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation

##
# Pre-defined configs
##
# isort: off
from isaaclab_assets.gripper.lanai_gripper import (
    LANAI_DEFAULT_FINGERTIPS_GRIPPER_CFG,
    LANAI_GSMINI_FINGERTIPS_GRIPPER_CFG,
    LANAI_TACTFUL_FINGERTIPS_GRIPPER_CFG,
)

# isort: on


def define_origins(num_origins: int, spacing: float) -> list[list[float]]:
    """Defines the origins of the the scene."""
    # create tensor based on number of environments
    env_origins = torch.zeros(num_origins, 3)
    # create a grid of origins
    num_cols = np.floor(np.sqrt(num_origins))
    num_rows = np.ceil(num_origins / num_cols)
    xx, yy = torch.meshgrid(torch.arange(num_rows), torch.arange(num_cols), indexing="xy")
    env_origins[:, 0] = spacing * xx.flatten()[:num_origins] - spacing * (num_rows - 1) / 2
    env_origins[:, 1] = spacing * yy.flatten()[:num_origins] - spacing * (num_cols - 1) / 2
    env_origins[:, 2] = 0.0
    # return the origins
    return env_origins.tolist()


def design_scene() -> tuple[dict, list[list[float]]]:
    """Designs the scene."""
    # Ground-plane
    cfg = sim_utils.GroundPlaneCfg()
    cfg.func("/World/defaultGroundPlane", cfg)
    # Lights
    cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
    cfg.func("/World/Light", cfg)

    # Create separate groups called "Origin1", "Origin2", "Origin3"
    # Each group will have a mount and a robot on top of it
    origins = define_origins(num_origins=3, spacing=0.25)

    # Origin 1 with Lanai default fingertips
    prim_utils.create_prim("/World/Origin1", "Xform", translation=origins[0])
    # -- Robot
    lanai_default = Articulation(LANAI_DEFAULT_FINGERTIPS_GRIPPER_CFG.replace(prim_path="/World/Origin1/Robot"))

    # # Origin 2 with Lanai tactful fingertips
    prim_utils.create_prim("/World/Origin2", "Xform", translation=origins[1])
    # # -- Robot
    lanai_tactful = Articulation(LANAI_TACTFUL_FINGERTIPS_GRIPPER_CFG.replace(prim_path="/World/Origin2/Robot"))

    # # Origin 3 with Lanai GSMINI fingertips
    prim_utils.create_prim("/World/Origin3", "Xform", translation=origins[2])
    # # -- Robot
    lanai_gsmini = Articulation(LANAI_GSMINI_FINGERTIPS_GRIPPER_CFG.replace(prim_path="/World/Origin3/Robot"))

    # return the scene information
    scene_entities = {
        "lanai_default": lanai_default,
        "lanai_tactful": lanai_tactful,
        "lanai_gsmini": lanai_gsmini,
    }
    return scene_entities, origins


def run_simulator(sim: sim_utils.SimulationContext, entities: dict[str, Articulation], origins: torch.Tensor):
    """Runs the simulation loop."""
    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    sim_time = 0.0
    count = 0
    # Start with hand open
    grasp_mode = 0
    # Simulate physics
    while simulation_app.is_running():
        # reset
        if count % 1000 == 0:
            # reset counters
            sim_time = 0.0
            count = 0
            # reset robots
            for index, robot in enumerate(entities.values()):
                # root state
                root_state = robot.data.default_root_state.clone()
                root_state[:, :3] += origins[index]
                robot.write_root_state_to_sim(root_state)
                # joint state
                joint_pos, joint_vel = robot.data.default_joint_pos.clone(), robot.data.default_joint_vel.clone()
                robot.write_joint_state_to_sim(joint_pos, joint_vel)
                # reset the internal state
                robot.reset()
            print("[INFO]: Resetting robots state...")
        # toggle grasp mode
        if count % 100 == 0:
            grasp_mode = 1 - grasp_mode
        # apply default actions to the hands robots
        for robot in entities.values():
            # generate joint positions
            joint_pos_target = robot.data.soft_joint_pos_limits[..., grasp_mode]
            # apply action to the robot
            robot.set_joint_position_target(joint_pos_target)
            # write data to sim
            robot.write_data_to_sim()
        # perform step
        sim.step()
        # update sim-time
        sim_time += sim_dt
        count += 1
        # update buffers
        for robot in entities.values():
            robot.update(sim_dt)


def main():
    """Main function."""

    # Initialize the simulation context
    sim = sim_utils.SimulationContext(sim_utils.SimulationCfg(dt=0.01))
    # Set main camera
    sim.set_camera_view(eye=[0.0, -0.5, 1.5], target=[0.0, -0.2, 0.5])
    # design scene
    scene_entities, scene_origins = design_scene()
    scene_origins = torch.tensor(scene_origins, device=sim.device)
    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")
    # Run the simulator
    run_simulator(sim, scene_entities, scene_origins)


if __name__ == "__main__":
    # run the main execution
    main()
    # close sim app
    simulation_app.close()
