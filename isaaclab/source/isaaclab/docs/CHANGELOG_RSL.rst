Changelog
---------

0.2.0 (2025-02-11)
~~~~~~~~~~~~~~~~~~~

Fixed
^^^^^

* Updates to new naming conventions and structure of IsaacLab 2.0


0.1.1 (2025-02-10)
~~~~~~~~~~~~~~~~~~~

Changed
^^^^^^^

* Remove ClassVar from :class:`~isaaclab.sensors.MultiMeshRayCaster` and
  :class:`~isaaclab.sensors.MultiMeshRayCasterCamera` to avoid issues with memory offloading after deletion


0.1.0 (2025-02-04)
~~~~~~~~~~~~~~~~~~~

Changed
^^^^^^^

* Restored the :class:`~isaaclab.sensors.RayCaster` of main and moved all classes related to the mulit-mesh
  ray-casting to individual classes (e.g., :class:`~isaaclab.sensors.MultiMeshRayCaster` and
  :class:`~isaaclab.sensors.MultiMeshRayCasterCamera`).

0.0.3 (2024-10-23)
~~~~~~~~~~~~~~~~~~~

Fixed
^^^^^

* Fixed number of meshes in the :class:`~isaaclab.sensors.RayCaster` when raycasting dynamically against a
  regex expression of multiple objects in the scene.


0.0.2 (2024-07-31)
~~~~~~~~~~~~~~~~~~~

Added
^^^^^

* Added serial to parallel actuator conversion for Dynaarm robot. This is implemented as its
  own actuator class :class:`isaaclab.actuators.ParallelDynaarmDCMotor`.


0.0.1 (2024-07-25)
~~~~~~~~~~~~~~~~~~~

Added
^^^^^

* Added multi-mesh ray-casting and tracking to the :class:`~isaaclab.sensors.RayCaster` and
  :class:`~isaaclab.sensors.RayCasterCamera`. This includes a new raycaster kernel to support
  an arbitrary number of meshes :func:`~isaaclab.utils.warp.raycast_dynamic_meshes`.
