# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Configuration for the ray-cast sensor."""


from dataclasses import MISSING

from isaaclab.utils import configclass

from .multi_mesh_ray_caster import MultiMeshRayCaster
from .ray_caster_cfg import RayCasterCfg


@configclass
class MultiMeshRayCasterCfg(RayCasterCfg):
    """Configuration for the ray-cast sensor."""

    @configclass
    class RaycastTargetCfg:
        """Configuration for different ray-cast targets."""

        target_prim_expr: str = MISSING
        """The regex to specify the target prim to ray cast against."""

        is_global: bool = False
        """Whether the target prim is a global object or exists for each environment instance. Defaults to False."""

    class_type: type = MultiMeshRayCaster

    mesh_prim_paths: list[str | RaycastTargetCfg] = MISSING
    """The list of mesh primitive paths to ray cast against."""

    track_mesh_transforms: bool = False
    """Whether the meshes transformations should be tracked. Defaults to False.

    Note:
        Not tracking the mesh transformations is recommended when the meshes are static to increase performance.
    """
