# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import torch
from typing import TYPE_CHECKING

from isaacsim.core.utils.types import ArticulationActions

from .actuator_pd import DCMotor

if TYPE_CHECKING:
    from .actuator_cfg import ParallelDynaarmDCMotorCfg


class ParallelDynaarmDCMotor(DCMotor):
    r"""Class for the parallel actuator model of the Dynaarm.

    The DynaArm robot uses a belt transmission. However, since the belt is not modeled in the
    the URDF, there is no real mechanical coupling between the two joints. This class helps
    model the parallel actuator model of the DynaArm.

    The simulator simulates a serial arm model, where the shoulder and elbow joints are
    connected in series. Let's denote this configuration with the generalized coordinates
    :math:`q = [q_{sh}, q_{el}]^T`. The parallel actuator model is a model where the shoulder
    and elbow joints are connected in parallel. Let's denote this configuration with the
    generalized coordinates :math:`\theta = [\theta_{sh}, \theta_{el}]^T`. These two models are
    related by the following equation:

    .. math::

        \theta_{sh} &= q_{sh} \\
        \theta_{el} &= q_{el} + q_{sh}

    Consequently, the velocity of the parallel model is given by:

    .. math::
        \dot{\theta} &= J_{q \rightarrow \theta} \dot{q} \\
                     &= \begin{bmatrix}
                            1 & 0 & 0 & 0 & 0 & 0 \\
                            0 & 1 & 0 & 0 & 0 & 0 \\
                            0 & 1 & 1 & 0 & 0 & 0 \\
                            0 & 0 & 0 & 1 & 0 & 0 \\
                            0 & 0 & 0 & 0 & 1 & 0 \\
                            0 & 0 & 0 & 0 & 0 & 1
                        \end{bmatrix} \dot{q}

    where the Jacobian :math:`J_{q \rightarrow \theta}` is used to convert the serial model
    velocity to the parallel model velocity.

    We compute the control action in the parallel model since it accounts for the coupling
    between the two joints. A serial model would assume that the two joints are independent
    which would yield incorrect control actions.

    Once the torque is computed for the parallel model, we need to convert it back to the
    serial model to apply it to the simulator. This is done using the following equation:

    .. math::

        \tau_{q} &= J_{q \rightarrow \theta}^T \tau_{\theta} \\
                 &= \begin{bmatrix}
                        1 & 0 & 0 & 0 & 0 & 0 \\
                        0 & 1 & 1 & 0 & 0 & 0 \\
                        0 & 0 & 1 & 0 & 0 & 0 \\
                        0 & 0 & 0 & 1 & 0 & 0 \\
                        0 & 0 & 0 & 0 & 1 & 0 \\
                        0 & 0 & 0 & 0 & 0 & 1
                    \end{bmatrix} \tau_{\theta}


    where :math:`\tau_{q}` is the torque in the serial model and :math:`\tau_{\theta}` is the
    torque in the parallel model. The derivation for above is based on the Principle of Virtual
    Work. Please refer to Jan Preisig's Master's Thesis for more details.

    Essentially, the above equations boil down to the following simple steps:

    1. Convert the serial model joint positions and velocities to the parallel model.

       .. math::

           \theta_{el} &= q_{el} + q_{sh} \\
           \dot{\theta_{el}} &= \dot{q_{el}} + \dot{q_{sh}}

    2. Compute the control action in the parallel model.

    3. Convert the control action back to the serial model.

       .. math::

           \tau_{q_{sh}} = \tau_{\theta_{sh}} + \tau_{\theta_{el}}

    """

    def __init__(self, cfg: ParallelDynaarmDCMotorCfg, *args, **kwargs):
        super().__init__(cfg, *args, **kwargs)

        self._sh_fle_index = self.joint_names.index(cfg.lower_drive_name)
        self._el_fle_index = self.joint_names.index(cfg.upper_drive_name)

    def compute(
        self, control_action: ArticulationActions, joint_pos: torch.Tensor, joint_vel: torch.Tensor
    ) -> ArticulationActions:
        # create a new action to store the computed joint commands
        coupled_control_action = ArticulationActions()
        coupled_control_action.joint_indices = control_action.joint_indices

        # serial to parallel conversion for the Dynaarm
        # note: all these clone operations add overhead but this makes it easy to understand.
        # -- joint velocities
        coupled_joint_vel = joint_vel.clone()
        coupled_joint_vel[:, self._el_fle_index] += coupled_joint_vel[:, self._sh_fle_index]
        # -- joint positions
        coupled_joint_pos = joint_pos.clone()
        coupled_joint_pos[:, self._el_fle_index] += coupled_joint_pos[:, self._sh_fle_index]
        # -- joint position target
        joint_pos_target = control_action.joint_positions.clone()
        joint_pos_target[:, self._el_fle_index] += joint_pos_target[:, self._sh_fle_index]
        coupled_control_action.joint_positions = joint_pos_target
        # -- joint velocity target
        joint_vel_target = control_action.joint_velocities.clone()
        joint_vel_target[:, self._el_fle_index] += joint_vel_target[:, self._sh_fle_index]
        coupled_control_action.joint_velocities = joint_vel_target
        # -- joint effort target
        joint_effort_target = control_action.joint_efforts.clone()
        joint_effort_target[:, self._sh_fle_index] -= joint_effort_target[:, self._el_fle_index]
        coupled_control_action.joint_efforts = joint_effort_target

        # Get the control action for the parallel model
        coupled_control_action = super().compute(coupled_control_action, coupled_joint_pos, coupled_joint_vel)

        # Convert the control action back to the serial model
        self.computed_effort[:, self._sh_fle_index] += self.computed_effort[:, self._el_fle_index]
        self.applied_effort[:, self._sh_fle_index] += self.applied_effort[:, self._el_fle_index]

        # Store the computed effort in the control action
        control_action.joint_efforts = self.applied_effort

        return control_action
