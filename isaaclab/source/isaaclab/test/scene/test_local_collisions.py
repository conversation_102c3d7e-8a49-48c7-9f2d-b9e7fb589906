# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""This script demonstrates how to use the interactive scene interface to setup a scene with multiple prims
    and local collision subgroups.

.. code-block:: bash

    # Usage
    ./isaaclab.sh -p source/extensions/omni.isaac.lab/test/scene/test_local_collisions.py

"""

"""Launch Isaac Sim Simulator first."""

import argparse

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="This script demonstrates how to use the scene interface.")
parser.add_argument("--headless", action="store_true", default=False, help="Force display off at all times.")
parser.add_argument("--num_envs", type=int, default=4, help="Number of environments to spawn.")
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(headless=args_cli.headless)
simulation_app = app_launcher.app

"""Rest everything follows."""

import isaaclab.sim as sim_utils
from isaaclab.assets import AssetBaseCfg, RigidObjectCfg
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sim import build_simulation_context
from isaaclab.utils import configclass
from isaaclab.sim import SimulationContext


@configclass
class LocalCollisionSceneCfg(InteractiveSceneCfg):
    """Configuration for a cart-pole scene."""

    # ground plane
    ground = AssetBaseCfg(prim_path="/World/defaultGroundPlane", spawn=sim_utils.GroundPlaneCfg())

    # lights
    dome_light = AssetBaseCfg(
        prim_path="/World/Light", spawn=sim_utils.DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75))
    )

    # Objects
    object_1 = RigidObjectCfg(
        prim_path="/World/envs/env_.*/Objects_1",
        spawn=sim_utils.CuboidCfg(
            size=(2, 2, 2),
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                disable_gravity=False,
            ),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
            ),
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.8, 0.2, 0.2)),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(-5, 0, 1)),
        collision_group=1,
    )
    object_2 = RigidObjectCfg(
        prim_path="/World/envs/env_.*/Objects_2",
        spawn=sim_utils.CuboidCfg(
            size=(1, 1, 1),
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                disable_gravity=False,
            ),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
            ),
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.8, 0.2, 0.2)),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(-5, 0, 5)),
        collision_group=1,
    )
    object_3 = RigidObjectCfg(
        prim_path="/World/envs/env_.*/Objects_3",
        spawn=sim_utils.CuboidCfg(
            size=(2, 2, 2),
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                disable_gravity=False,
            ),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
            ),
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.2, 0.8, 0.2)),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(5, 0, 1)),
        collision_group=0,
    )
    object_4 = RigidObjectCfg(
        prim_path="/World/envs/env_.*/Objects_4",
        spawn=sim_utils.CuboidCfg(
            size=(1, 1, 1),
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                disable_gravity=False,
            ),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
            ),
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.2, 0.8, 0.2)),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(5, 0, 5)),
        collision_group=0,
    )

    object_5 = RigidObjectCfg(
        prim_path="/World/envs/env_.*/Objects_5",
        spawn=sim_utils.CuboidCfg(
            size=(1, 1, 1),
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                disable_gravity=False,
            ),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
            ),
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.2, 0.2, 0.8)),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(0, 0, 5)),
        collision_group=2,
    )


def run_simulator(sim: SimulationContext, scene: InteractiveScene):
    """Runs the simulation loop."""
    # Extract scene entities
    objects = {f"object_{i}": scene[f"object_{i}"] for i in range(1, 6)}
    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    count = 0
    # root state
    root_states = {}
    for i, obj in enumerate(objects.values(), start=1):
        root_state = obj.data.default_root_state.clone()
        root_state[:, :3] += scene.env_origins
        root_states[f"object_{i}"] = root_state
    # Simulation loop
    while simulation_app.is_running():
        # Reset
        if count % 500 == 0:
            # reset counter
            count = 0
            # Perform circular swapping of root states (2 -> 4 -> 5 -> 2)
            root_states["object_2"], root_states["object_4"], root_states["object_5"] = (
                root_states["object_5"],
                root_states["object_2"],
                root_states["object_4"],
            )
            # Reset the scene entities with updated root states
            for i, obj in enumerate(objects.values(), start=1):
                obj.write_root_state_to_sim(root_states[f"object_{i}"])
            # clear internal buffers
            scene.reset()
            print("[INFO]: Resetting cubes state...")
        # -- write data to sim
        scene.write_data_to_sim()
        # Perform step
        sim.step()
        # Increment counter
        count += 1
        # Update buffers
        scene.update(sim_dt)


def main():
    """Main function."""
    # Load kit helper
    sim = SimulationContext(sim_utils.SimulationCfg(dt=0.025))
    # Set main camera
    sim.set_camera_view([0, -40, 10.0], [0.0, 2.0, 0.0])
    # Design scene
    scene_cfg = LocalCollisionSceneCfg(num_envs=args_cli.num_envs, env_spacing=20.0)
    scene = InteractiveScene(scene_cfg)
    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")
    # Run the simulator
    run_simulator(sim, scene)


if __name__ == "__main__":
    # run the main function
    main()
    # close sim app
    simulation_app.close()