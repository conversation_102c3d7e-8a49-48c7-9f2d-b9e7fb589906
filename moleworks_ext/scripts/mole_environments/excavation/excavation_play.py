# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Script to play a checkpoint if an RL Excavation agent and log data"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse

from isaaclab.app import AppLauncher

# local imports
import os
import sys

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Now you can import cli_args as if it was on the same level
import cli_args  # isort: skip

# add argparse arguments
parser = argparse.ArgumentParser(description="Train an RL agent with RSL-RL.")
parser.add_argument(
    "--num_envs", type=int, default=1, help="Number of environments to simulate."
)
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument(
    "--seed", type=int, default=None, help="Seed used for the environment"
)
parser.add_argument(
    "--tags", nargs="+", type=str, default=[], help="Tags used for the run."
)
parser.add_argument(
    "--time", type=float, default=50, help="Duration of each episode in seconds."
)
# append RSL-RL cli arguments
cli_args.add_rsl_rl_args(parser)
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()
# Override command line arguments
args_cli.task = "Isaac-m545-digging"
# args_cli.num_envs = 1
# args_cli.headless = True

# launch isaaclab app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

from isaaclab_tasks.utils import get_checkpoint_path, parse_env_cfg
from isaaclab.utils.assets import retrieve_file_path

"""Rest everything follows."""
import gymnasium as gym
import os
import torch
import traceback

import carb
from rsl_rl.runners import OnPolicyRunner
from datetime import datetime

# import pickle # Removed unused import

# Task-specific imports
import moleworks_ext.tasks  # noqa: F401 - Registers the task
from moleworks_ext.tasks.excavation.excavation_utils.excavation_logger import Logger

# Removed unused import
# from moleworks_ext.tasks.excavation.excavation_utils.excavation_arg_helper import (
#     override_env_cfg_with_args,
# )
from pprint import pprint

# Isaac Lab imports
from isaaclab_rl.rsl_rl import RslRlOnPolicyRunnerCfg, RslRlVecEnvWrapper


def main():
    """Play with Excavation agent."""
    # Make the environment
    agent_cfg: RslRlOnPolicyRunnerCfg = cli_args.parse_rsl_rl_cfg(
        args_cli.task, args_cli
    )
    log_root_path = os.path.join("logs", "rsl_rl", agent_cfg.experiment_name)
    log_root_path = os.path.abspath(log_root_path)
    if agent_cfg.load_run == ".*":
        runs = [
            d
            for d in os.listdir(log_root_path)
            if os.path.isdir(os.path.join(log_root_path, d))
        ]
        if not runs:
            raise FileNotFoundError(
                f"No runs found in {log_root_path} when searching for the latest run."
            )
        latest_run = sorted(runs)[-1]
        run_dir_name = latest_run
        agent_cfg.load_run = run_dir_name
        print(f"[INFO] Found latest run: {run_dir_name}")
    elif agent_cfg.load_run is None:
        raise ValueError(
            "agent_cfg.load_run is None. A run directory name or '.*' pattern is expected."
        )
    else:
        run_dir_name = agent_cfg.load_run
        print(f"[INFO] Using run specified in agent config: {run_dir_name}")
    run_path = os.path.join(log_root_path, run_dir_name)
    if not os.path.exists(run_path):
        raise FileNotFoundError(
            f"Run directory '{run_dir_name}' not found at: {run_path}"
        )
    if hasattr(args_cli, "checkpoint") and args_cli.checkpoint:
        resume_path = retrieve_file_path(args_cli.checkpoint)
        if not resume_path or not os.path.isfile(resume_path):
            raise FileNotFoundError(
                f"Checkpoint not found or is not a file at specified path: {args_cli.checkpoint}"
            )
        print(f"[INFO] Using specified checkpoint: {resume_path}")
        if os.path.dirname(os.path.dirname(resume_path)) != run_path:
            print(
                f"[WARNING] The specified checkpoint '{resume_path}' does not seem to belong to the derived run path '{run_path}'."
            )
    else:
        resume_path = get_checkpoint_path(
            log_root_path, agent_cfg.load_run, agent_cfg.load_checkpoint
        )
        if not resume_path:
            raise FileNotFoundError(
                f"Checkpoint matching pattern '{agent_cfg.load_checkpoint}' not found in run '{agent_cfg.load_run}' located at: {run_path}"
            )
        print(
            f"[INFO] Using checkpoint from run '{agent_cfg.load_run}': {os.path.basename(resume_path)}"
        )

    # Override Env Cfg with Play Args
    # Load train args
    # train_args_path = os.path.join(run_path, "params/env.pkl")
    # if os.path.exists(train_args_path):
    #     with open(train_args_path, "rb") as f:
    #         train_args = pickle.load(f)
    #     print(f"[INFO] Loaded training arguments from: {train_args_path}")
    # else:
    #     print("[WARNING] Training arguments file not found in run directory. Using default environment config.")
    #     train_args = argparse.Namespace()
    env_cfg = parse_env_cfg(args_cli.task, num_envs=args_cli.num_envs)
    # env_cfg = override_env_cfg_with_args(env_cfg, train_args)

    # More overriding
    env_cfg.decimation = 4
    env_cfg.sim.dt = 0.0375
    # env_cfg.max_depth_height.theta = 0.5
    env_cfg.reset.sample_soil = True
    env_cfg.send_timeouts = False
    env_cfg.terminations_excavation.disable_negative_termination = (
        False  # args.disable_neg_terms
    )
    env_cfg.reset.only_above_soil = True

    # Set reset parameters
    # env_cfg.soil_parameters.type = "S_0_0"
    # env_cfg.soil_parameters.type = "random"

    env_cfg.reset.sample_max_depth = True
    env_cfg.reset.sample_pullup_dist = True
    env_cfg.reset.sample_obstacles = False
    env_cfg.reset.only_above_soil = True
    env_cfg.reset.fixed_config = False

    # create isaac environment
    env = gym.make(args_cli.task, cfg=env_cfg, headless=args_cli.headless)
    # wrap around environment for rsl-rl
    env = RslRlVecEnvWrapper(env)
    # Set curriculum, need to reset after
    level = torch.tensor([2000.0], device=env.unwrapped.device)
    env.unwrapped.curriculum_excavation.set_level_and_update(level)
    env.unwrapped.train = False

    # load previously trained model
    ppo_runner = OnPolicyRunner(
        env, agent_cfg.to_dict(), log_dir=None, device=agent_cfg.device
    )
    ppo_runner.load(resume_path)

    # obtain the trained policy for inference
    policy = ppo_runner.get_inference_policy(device=env.unwrapped.device)

    # Settings
    num_steps = int(
        args_cli.time / (env.unwrapped.physics_dt * env.unwrapped.cfg.decimation)
    )  # t = 20

    # Logger
    # create logdir
    play_log_dir = os.path.join(
        run_path,
        "play_" + os.path.splitext(os.path.basename(resume_path))[0],
        datetime.now().strftime("%y_%m_%d_%H_%M_%S_") + "benchmarking",
    )
    os.makedirs(play_log_dir)
    robot_indices = [0]  # Indices of robots to be logged
    loggers = [Logger(env.unwrapped, num_steps + 1, idx) for idx in robot_indices]

    # reset environment, necessary after curriculum update
    env.reset()
    obs, _ = env.get_observations()

    # Log initial state
    for logger in loggers:
        logger.log_states(0, obs)

    # simulate environment
    # run everything in inference mode
    with torch.inference_mode():
        for i in range(num_steps):
            # agent stepping
            actions = policy(obs)
            # env stepping
            obs, rewards, dones, infos = env.step(actions)

            # Log states after stepping
            for logger in loggers:
                logger.log_states(i + 1, obs)

            # Live termination statistics
            if dones.any():
                print("i: ", i)
                print("reward: ", rewards)
                pprint(infos["episode_pos_term_counts"])
                pprint(infos["episode_neg_term_counts"])

    for idx, logger in enumerate(loggers):
        logger.log_dones()
        logger.plot_states(show=False)
        logger.save_for_ros_plotter(play_log_dir)
        plot_name = f"plot_{idx}.pdf"
        logger.save_plots(play_log_dir, file_name=plot_name)

    input("hit [ENTER] to exit")
    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
