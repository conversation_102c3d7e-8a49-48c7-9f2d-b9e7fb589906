# === Soil height ====
def soil_height(env) -> torch.Tensor:
    if env.cfg.soil_model_cfg.type == "2d":
        # Use existing 2D implementation
        x_pos = env.m545_measurements.bucket_edge_pos[:, 0]  # in base frame
        heights = []
        for i in range(5):
            dist = i * 0.5 + 0.5
            h = env.soil.soil_height.get_height(x_pos + dist)
            heights.append(h)
        return torch.cat(heights, dim=-1)
    else:
        # Use 3D implementation with raycasting
        return env.soil.get_ray_observations()


def max_depth(env) -> torch.Tensor:
    if env.cfg.soil_model_cfg.type == "2d":
        # Use existing 2D implementation
        x_pos = env.m545_measurements.bucket_edge_pos[:, 0]  # in base frame
        max_depths = []
        for i in range(5):
            dist = i * 0.5 + 0.5
            h = env.soil.max_depth_height.get_height(x_pos + dist)
            max_depths.append(h)
        return torch.cat(max_depths, dim=-1)
    else:
        # For 3D, we need to sample the max depth heights at specific points
        import torch
        
        x_pos = env.m545_measurements.bucket_edge_pos[:, 0]  # in base frame
        
        # Create empty tensor for results
        device = env.soil.device
        n_envs = env.soil.n_envs
        max_depths = []
        
        # Sample depths at 5 points in front of the bucket
        for i in range(5):
            dist = i * 0.5 + 0.5
            
            # Process each environment separately
            env_depths = []
            for env_idx in range(n_envs):
                # Create a single-point query for this environment
                x_val = (x_pos[env_idx] + dist).item()
                
                # Create query tensors with shape [1, 1]
                query_x = torch.tensor([[x_val]], device=device)
                query_y = torch.tensor([[0.0]], device=device)  # Assume central line (y=0)
                
                # Get height at this point for this environment
                height = env.soil.get_max_depth_height_at_point(query_x, query_y)[env_idx, 0]
                env_depths.append(height)
            
            # Combine all environment depths for this point
            point_depths = torch.stack(env_depths)
            max_depths.append(point_depths)
            
        return torch.cat(max_depths, dim=-1) 