# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Dict, Tuple, Union, Sequence

import torch


class SoilModelBase(ABC):
    """Abstract base class for soil models.
    
    This class defines the interface that all soil models must implement.
    It ensures compatibility between different soil model implementations
    while allowing for specialized implementations.
    """
    
    @abstractmethod
    def post_init(self, env):
        """Initialize the soil model with the environment.
        
        Args:
            env: The excavation environment
        """
        pass
    
    @abstractmethod
    def update(self):
        """Update the soil model state."""
        pass
    
    @abstractmethod
    def update_1(self, idxs=...):
        """First update stage of the soil model.
        
        Args:
            idxs: Indices of environments to update
        """
        pass
    
    @abstractmethod
    def update_2(self):
        """Second update stage of the soil model."""
        pass
    
    @abstractmethod
    def reset(self, idxs=...):
        """Reset the soil model state for specified environments.
        
        Args:
            idxs: Indices of environments to reset
        """
        pass
    
    @abstractmethod
    def sample(self, idxs=...):
        """Sample soil parameters for specified environments.
        
        Args:
            idxs: Indices of environments to sample parameters for
        """
        pass
    
    @abstractmethod
    def is_state_invalid(self, idxs=...):
        """Check if the soil state is invalid.
        
        Args:
            idxs: Indices of environments to check
            
        Returns:
            Boolean tensor indicating if each environment's state is invalid
        """
        pass
    
    @abstractmethod
    def get_soil_failure_ang(self):
        """Get the soil failure angle.
        
        Returns:
            Tensor containing soil failure angles
        """
        pass
    
    @abstractmethod
    def get_ssp_ang_to_soil(self):
        """Get the angle of the secondary separation plate relative to soil.
        
        Returns:
            Tensor containing angles
        """
        pass
    
    @abstractmethod
    def get_ssp_L(self):
        """Get the length of the secondary separation plate.
        
        Returns:
            Tensor containing lengths
        """
        pass
    
    @abstractmethod
    def get_ssp_L_max(self):
        """Get the maximum length of the secondary separation plate.
        
        Returns:
            Tensor containing maximum lengths
        """
        pass
    
    @abstractmethod
    def get_ssp_L_max_no_bucket(self):
        """Get the maximum length of the SSP without considering the bucket.
        
        Returns:
            Tensor containing maximum lengths
        """
        pass 