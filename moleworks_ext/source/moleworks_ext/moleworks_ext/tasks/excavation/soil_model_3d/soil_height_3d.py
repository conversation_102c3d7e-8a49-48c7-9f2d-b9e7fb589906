from __future__ import annotations

import torch
import numpy as np  # noqa: F401
from typing import Optional, Union, Tuple, List  # noqa: F401


class SoilHeight3D:
    def __init__(self, cfg, cfg_name, device, num_envs, upper_limit=None):
        """Initialize the 3D soil height field.

        Args:
            cfg: Configuration object
            cfg_name: Name of config section for this height field
            device: PyTorch device
            num_envs: Number of environments
            upper_limit: Optional upper limit height field
        """
        # Get configuration values
        soil_cfg = getattr(cfg, cfg_name)

        # Store parameters
        self.cfg = cfg
        self.cfg_name = cfg_name
        self.device = device
        self.n_envs = num_envs
        self.class_cfg = vars(self.cfg)[self.cfg_name]
        self.upper_limit = upper_limit

        # Extract terrain parameters
        self.x_min = soil_cfg.x_min
        self.x_max = soil_cfg.x_max
        self.y_min = soil_cfg.y_min
        self.y_max = soil_cfg.y_max
        self.z_min = soil_cfg.z_min
        self.z_max = soil_cfg.z_max
        self.terrain_type = soil_cfg.type

        # Get 3D grid parameters
        self.grid_size_x = cfg.soil_model_cfg.model_3d.grid_size_x
        self.grid_size_y = cfg.soil_model_cfg.model_3d.grid_size_y
        self.cell_size = cfg.soil_model_cfg.model_3d.cell_size

        # Scale and theta for RBF
        self.scale = self.class_cfg.scale
        self.theta = self.class_cfg.theta

        # Initialize grid coordinates
        self.x_grid = torch.linspace(
            self.x_min, self.x_max, self.grid_size_x, device=self.device
        )
        self.y_grid = torch.linspace(
            self.y_min, self.y_max, self.grid_size_y, device=self.device
        )

        # Create meshgrid for fast queries
        self.x_coords, self.y_coords = torch.meshgrid(
            self.x_grid, self.y_grid, indexing="ij"
        )

        # Calculate grid spacing for interpolation
        self.x_dist = (self.x_max - self.x_min) / (self.grid_size_x - 1)
        self.y_dist = (self.y_max - self.y_min) / (self.grid_size_y - 1)

        # Initialize height field for each environment
        self.height_field = torch.zeros(
            (self.n_envs, self.grid_size_x, self.grid_size_y), device=self.device
        )

        # Pre-compute constants for efficiency - MUST be before terrain initialization
        self.two_tensor = torch.tensor(2.0, device=self.device)
        self.half_tensor = torch.tensor(0.5, device=self.device)
        self.one_tensor = torch.tensor(1.0, device=self.device)
        self.neg_one_tensor = torch.tensor(-1.0, device=self.device)

        # Pre-compute normalization factors for coordinate transformation
        self.x_range = self.x_max - self.x_min
        self.y_range = self.y_max - self.y_min
        self.x_norm_factor = self.two_tensor / self.x_range
        self.y_norm_factor = self.two_tensor / self.y_range

        # RBF terrain parameters
        if self.terrain_type == "rbf":
            self.rbf_min_resolution = soil_cfg.min_resolution
            self.rbf_theta = soil_cfg.theta
            self.rbf_scale = soil_cfg.scale
            # Precompute RBF correlation matrix and decomposition for efficiency
            self._compute_rbf_matrix()

        # Slope terrain parameters
        elif self.terrain_type == "slope":
            self.slope_start_height = soil_cfg.slope_start_height
            self.slope_start = soil_cfg.slope_start  # [m] from excavator
            self.slope_ang = soil_cfg.slope_ang
            self.slope_x_len = soil_cfg.slope_x_len  # [m]

        # X-only terrain parameters
        elif self.terrain_type == "x_only":
            self.x_only_scale = soil_cfg.scale
            self.x_only_theta = soil_cfg.theta
            # Precompute RBF matrix for x-direction only
            self._compute_x_only_matrix()

        # Initialize terrain
        self.sample()

    def _expand_coordinates_to_envs(self, x: torch.Tensor, y: torch.Tensor, n_envs: int):
        """Efficiently expand coordinate tensors to match environment dimensions.

        Args:
            x: X coordinates tensor
            y: Y coordinates tensor
            n_envs: Number of environments

        Returns:
            Tuple of (x_expanded, y_expanded, n_points) where tensors have shape [n_envs, n_points]
        """
        # Ensure x and y have the correct shape [n_envs, n_points]
        if x.dim() == 1:
            x = x.unsqueeze(0).expand(n_envs, -1)
            y = y.unsqueeze(0).expand(n_envs, -1)
        elif x.dim() > 2:
            x = x.reshape(n_envs, -1)
            y = y.reshape(n_envs, -1)
        elif x.shape[0] != n_envs:
            if x.shape[0] == 1:
                x = x.expand(n_envs, -1)
                y = y.expand(n_envs, -1)
            else:
                # Handle case where x/y contains data for requested env_ids contiguously
                total_points_in_x = x.numel()
                points_per_env = total_points_in_x // n_envs
                if points_per_env * n_envs == total_points_in_x:
                    x = x.reshape(n_envs, points_per_env)
                    y = y.reshape(n_envs, points_per_env)
                else:
                    raise ValueError(
                        f"Input x/y shape {x.shape} incompatible with {n_envs} environments."
                    )

        n_points = x.shape[1]
        return x, y, n_points

    def _normalize_coordinates(self, x: torch.Tensor, y: torch.Tensor):
        """Efficiently normalize coordinates to [-1, 1] range using pre-computed factors.

        Args:
            x: X coordinates tensor
            y: Y coordinates tensor

        Returns:
            Tuple of (x_normalized, y_normalized)
        """
        # Clamp coordinates to grid bounds
        x_clamped = torch.clamp(x, self.x_min, self.x_max - 1e-5)
        y_clamped = torch.clamp(y, self.y_min, self.y_max - 1e-5)

        # Normalize coordinates to [-1, 1] using pre-computed factors
        x_normalized = (x_clamped - self.x_min) * self.x_norm_factor - self.one_tensor
        y_normalized = (y_clamped - self.y_min) * self.y_norm_factor - self.one_tensor

        return x_normalized, y_normalized

    def _compute_rbf_matrix(self):
        """Precompute RBF correlation matrix and its eigen-decomposition.
        This significantly improves performance for terrain generation.
        """
        if self.terrain_type != "rbf":
            return

        # Flatten the grid coordinates
        xy_coords = torch.stack(
            (self.x_coords.flatten(), self.y_coords.flatten()), dim=-1
        )  # shape [grid_x * grid_y, 2]

        # Compute pairwise distances between all grid points
        dist_matrix = torch.cdist(xy_coords, xy_coords, p=2)

        # Apply RBF kernel to get covariance matrix - optimized with pre-computed values
        if not hasattr(self, '_rbf_theta_squared_doubled'):
            self._rbf_theta_squared_doubled = self.two_tensor * self.rbf_theta**2
        if not hasattr(self, '_stability_epsilon'):
            self._stability_epsilon = torch.tensor(1e-5, device=self.device)

        self.cov_mat = torch.exp(-(dist_matrix**2) / self._rbf_theta_squared_doubled)

        # Add small diagonal term for numerical stability
        self.cov_mat = (
            self.cov_mat + torch.eye(self.cov_mat.shape[0], device=self.device) * self._stability_epsilon
        )

        # Compute eigen-decomposition for efficient sampling
        eig_vals, eig_vecs = torch.linalg.eigh(self.cov_mat)

        # Clip eigenvalues to avoid numerical issues
        eig_vals = torch.clamp(eig_vals, min=0.0)

        # Precompute transformation matrix for efficient sampling
        self.norm_transform = eig_vecs @ torch.diag(torch.sqrt(eig_vals))

    def _compute_x_only_matrix(self):
        """Precompute RBF correlation matrix for x-direction only terrain.
        This significantly improves performance for terrain generation.
        """
        if self.terrain_type != "x_only":
            return

        # Use only x coordinates for RBF computation
        x_coords = self.x_grid.unsqueeze(-1)  # shape [grid_x, 1]

        # Compute pairwise distances between all x points
        dist_matrix = torch.cdist(x_coords, x_coords, p=2)

        # Apply RBF kernel to get covariance matrix - optimized
        if not hasattr(self, '_x_only_theta_squared_doubled'):
            self._x_only_theta_squared_doubled = self.two_tensor * self.x_only_theta**2

        self.cov_mat_x = torch.exp(-(dist_matrix**2) / self._x_only_theta_squared_doubled)

        # Add small diagonal term for numerical stability
        self.cov_mat_x = (
            self.cov_mat_x
            + torch.eye(self.cov_mat_x.shape[0], device=self.device) * self._stability_epsilon
        )

        # Compute eigen-decomposition for efficient sampling
        eig_vals, eig_vecs = torch.linalg.eigh(self.cov_mat_x)

        # Clip eigenvalues to avoid numerical issues
        eig_vals = torch.clamp(eig_vals, min=0.0)

        # Precompute transformation matrix for efficient sampling
        self.norm_transform_x = eig_vecs @ torch.diag(torch.sqrt(eig_vals))

    def sample(self, idxs=...):
        """Sample new terrain heights for the specified environments.

        Args:
            idxs: Indices of environments to sample
        """
        if idxs is ...:
            idxs = range(self.n_envs)

        if self.terrain_type == "rbf":
            self._sample_rbf_terrain(idxs)
        elif self.terrain_type == "slope":
            self._sample_slope_terrain(idxs)
        elif self.terrain_type == "x_only":
            self._sample_x_only_terrain(idxs)

        # Apply upper limit if provided
        if self.upper_limit is not None:
            self.height_field[idxs] = torch.min(
                self.height_field[idxs],
                self.upper_limit.height_field[idxs] - 0.05,  # 5cm margin
            )

    def _sample_rbf_terrain(self, idxs):
        """Sample RBF-based terrain for the specified environments.
        Vectorized implementation that leverages precomputed matrices.

        Args:
            idxs: Indices of environments to sample
        """
        # Determine batch size for sampling
        if idxs is ...:
            batch_size = self.n_envs
        else:
            batch_size = len(idxs)

        # Generate random vectors for each environment
        rand_vectors = torch.randn(
            batch_size, self.grid_size_x * self.grid_size_y, device=self.device
        )

        # Apply transformation to get correlated heights
        # Using matrix multiplication for efficiency
        height_fields_flat = (self.norm_transform @ rand_vectors.T).T * self.rbf_scale

        # Reshape to grid dimensions
        height_fields = height_fields_flat.view(
            batch_size, self.grid_size_x, self.grid_size_y
        )

        # Apply offset based on configuration
        if hasattr(self.class_cfg, "offset") and self.class_cfg.offset is not None:
            if isinstance(self.class_cfg.offset, (int, float)):
                # Constant offset
                offset = self.class_cfg.offset
            else:
                # Random offset within range
                z_range = self.z_max - self.z_min
                offset = (
                    torch.rand(batch_size, 1, 1, device=self.device) * z_range
                    + self.z_min
                )

            # Apply offset to height field
            height_fields = height_fields + offset

        # Assign to height field
        self.height_field[idxs] = height_fields

    def _sample_slope_terrain(self, idxs):
        """Sample slope-based terrain for the specified environments.
        Vectorized implementation for all environments at once.

        Args:
            idxs: Indices of environments to sample
        """
        # Get batch size
        if idxs is ...:
            batch_size = self.n_envs
            idxs = range(self.n_envs)
        else:
            batch_size = len(idxs)

        # Convert slope angle to tensor - cache for efficiency
        if not hasattr(self, '_slope_ang_tensor'):
            self._slope_ang_tensor = torch.tensor(self.slope_ang, device=self.device)
        slope_ang_tensor = self._slope_ang_tensor

        # Compute heights based on slope conditions - vectorized implementation
        # Expand x coordinates for broadcasting
        x_expanded = self.x_coords.unsqueeze(0).expand(batch_size, -1, -1)

        # Create height field tensor for batch
        height_field = torch.zeros_like(x_expanded)

        # Compute masks for different regions
        # Flat area before slope
        mask_before = x_expanded < self.slope_start

        # Sloped area
        mask_slope = (x_expanded >= self.slope_start) & (
            x_expanded < self.slope_start + self.slope_x_len
        )

        # Flat area after slope
        mask_after = x_expanded >= (self.slope_start + self.slope_x_len)

        # Apply heights to each region
        height_field = torch.zeros_like(x_expanded)

        # Flat area before slope
        height_field = torch.where(
            mask_before,
            torch.full_like(height_field, self.slope_start_height),
            height_field,
        )

        # Sloped area
        dist_from_start = torch.where(
            mask_slope, x_expanded - self.slope_start, torch.zeros_like(x_expanded)
        )
        slope_height = self.slope_start_height + dist_from_start * torch.tan(
            slope_ang_tensor
        )
        height_field = torch.where(mask_slope, slope_height, height_field)

        # Flat area after slope
        max_slope_height = self.slope_start_height + self.slope_x_len * torch.tan(
            slope_ang_tensor
        )
        height_field = torch.where(mask_after, max_slope_height, height_field)

        # Assign to height field for each environment
        for i, env_idx in enumerate(idxs):
            self.height_field[env_idx] = height_field[i]

    def _sample_x_only_terrain(self, idxs):
        """Sample terrain that only varies in x-direction.
        Uses RBF sampling for x-direction and replicates the same profile for all y-coordinates.

        Args:
            idxs: Indices of environments to sample
        """
        # Determine batch size for sampling
        if idxs is ...:
            batch_size = self.n_envs
        else:
            batch_size = len(idxs)

        # Generate random vectors for each environment
        rand_vectors = torch.randn(batch_size, self.grid_size_x, device=self.device)

        # Apply transformation to get correlated heights for x-direction only
        height_profiles = (self.norm_transform_x @ rand_vectors.T).T * self.x_only_scale

        # Apply offset based on configuration
        if hasattr(self.class_cfg, "offset") and self.class_cfg.offset is not None:
            if isinstance(self.class_cfg.offset, (int, float)):
                # Constant offset
                offset = self.class_cfg.offset
            else:
                # Random offset within range
                z_range = self.z_max - self.z_min
                offset = (
                    torch.rand(batch_size, 1, device=self.device) * z_range + self.z_min
                )

            # Apply offset to height profiles
            height_profiles = (
                height_profiles + offset
            )  # Shape [batch_size, grid_size_x]

        # Create height field by replicating the x-profile for all y-coordinates
        # Reshape to [batch_size, grid_size_x, 1]
        height_profiles = height_profiles.unsqueeze(
            -1
        )  # Use unsqueeze(-1) instead of unsqueeze(1)

        # Expand to [batch_size, grid_size_x, grid_size_y]
        height_fields = height_profiles.expand(
            -1, -1, self.grid_size_y
        )  # Expand the last dimension

        # Assign to height field
        self.height_field[idxs] = height_fields

    def get_height_field(self):
        return self.height_field

    def get_height(self, x: torch.Tensor, y: torch.Tensor, env_ids=...) -> torch.Tensor:
        """Get the height of the soil at specific points using bilinear interpolation.

        Args:
            x: X coordinates. Shape depends on env_ids.
            y: Y coordinates. Shape depends on env_ids.
            env_ids: Optional environment indices. Use ... for all, an int for a single environment, or a tensor/list of ints for specific environments.

        Returns:
            Heights. Shape matches the effective number of environments and points queried.
        """
        # Handle different env_ids cases efficiently
        single_env_id = None

        if isinstance(env_ids, (int, float)):
            # Single environment case
            single_env_id = int(env_ids)
            hf = self.height_field[single_env_id]  # Shape [grid_size_x, grid_size_y]
            n_envs = 1
            x = x.squeeze()  # Ensure shape [n_points]
            y = y.squeeze()  # Ensure shape [n_points]
            n_points = x.shape[0]
            hf_expanded = hf.unsqueeze(0).unsqueeze(0)

        elif isinstance(env_ids, (torch.Tensor, list)) and env_ids is not ...:
            # Specific environments case
            if isinstance(env_ids, list):
                env_ids_tensor = torch.tensor(env_ids, device=self.device, dtype=torch.long)
                n_envs = len(env_ids)
            elif isinstance(env_ids, torch.Tensor):
                env_ids_tensor = env_ids
                n_envs = env_ids_tensor.shape[0]
            else:
                raise ValueError(f"Unsupported env_ids type: {type(env_ids)}")

            if n_envs == 0:
                return torch.empty((0, x.shape[1] if x.dim() > 1 else 0), device=self.device)

            hf = self.height_field.index_select(0, env_ids_tensor)

            x, y, n_points = self._expand_coordinates_to_envs(x, y, n_envs)
            hf_expanded = hf.unsqueeze(1)

        elif env_ids is ...:
            # All environments case
            hf = self.height_field
            n_envs = self.n_envs
            if n_envs == 0:
                return torch.empty((0, x.shape[1] if x.dim() > 1 else 0), device=self.device)

            x, y, n_points = self._expand_coordinates_to_envs(x, y, n_envs)
            hf_expanded = hf.unsqueeze(1)

        else:
            raise ValueError(f"Unsupported type for env_ids: {type(env_ids)}")

        # Normalize coordinates using optimized helper
        x_normalized, y_normalized = self._normalize_coordinates(x, y)

        # Create grid for grid_sample - optimized for all cases
        grid = torch.stack([y_normalized, x_normalized], dim=-1).view(
            n_envs, 1, n_points, 2
        )

        # Bilinear interpolation using grid_sample
        sampled = torch.nn.functional.grid_sample(
            hf_expanded,  # Shape [N, 1, H=grid_size_x, W=grid_size_y]
            grid,  # Shape [N, 1, n_points, 2] with coords (y_norm, x_norm)
            mode="bilinear",
            padding_mode="border",
            align_corners=True,
        )  # Output shape [N, 1, 1, n_points]

        # Squeeze to get desired output shape
        if single_env_id is not None:
            # Single env case: return 1D tensor [n_points]
            heights = sampled.view(-1)
        else:
            # Multi-env case: return [N, n_points]
            heights = sampled.squeeze(1).squeeze(1)

        return heights

    def get_angle_to_world(
        self, x: torch.Tensor, y: torch.Tensor, env_ids=...
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Get the angle of the soil surface at specific points.
        Fully vectorized implementation using numerical gradient computation.
        Args:
            x: X coordinates in one of these formats:
               - [batch_size]: single batch of points for all environments
               - [n_envs, n_points]: specific points for each environment
            y: Y coordinates in the same format as x
            env_ids: Optional environment indices to retrieve angles for
                     Use ... (ellipsis) to retrieve angles for all environments

        Returns:
            Tuple of (angle_x, angle_y) representing the soil slope angles in radians
            along the x and y directions at the specified points with same shape as input
        """

        # Use adaptive delta based on grid size for better accuracy - optimized with pre-computed values
        delta_x = max(self.x_range / (self.grid_size_x * 10), 0.005)
        delta_y = max(self.y_range / (self.grid_size_y * 10), 0.005)

        # Clamp input coordinates to valid range with margin for central difference
        x_clamped = torch.clamp(x, self.x_min + delta_x, self.x_max - delta_x)
        y_clamped = torch.clamp(y, self.y_min + delta_y, self.y_max - delta_y)

        # Create offset coordinates for central difference method
        x_plus = x_clamped + delta_x
        x_minus = x_clamped - delta_x
        y_plus = y_clamped + delta_y
        y_minus = y_clamped - delta_y

        # Get heights at all points - passing env_ids to get_height
        # Note: We don't need h_center for central difference method
        h_x_plus = self.get_height(x_plus, y_clamped, env_ids)
        h_x_minus = self.get_height(x_minus, y_clamped, env_ids)
        h_y_plus = self.get_height(x_clamped, y_plus, env_ids)
        h_y_minus = self.get_height(x_clamped, y_minus, env_ids)

        # Compute gradients using central difference (more accurate than one-sided) - optimized
        dx = (h_x_plus - h_x_minus) / (self.two_tensor * delta_x)
        dy = (h_y_plus - h_y_minus) / (self.two_tensor * delta_y)

        # Compute angles (arctan of the gradients)
        angles_x = torch.atan(dx)
        angles_y = torch.atan(dy)

        return angles_x, angles_y

    def integrate(
        self,
        x0: torch.Tensor,
        y0: torch.Tensor,
        x1: torch.Tensor,
        y1: torch.Tensor,
        num_samples: int = 5,
        env_ids=...,
    ) -> torch.Tensor:
        """Calculate the volume under the soil surface between two points.
        Uses efficient vectorized grid sampling for volume integration.

        Args:
            x0, y0: Coordinates of the first point [batch_size]
            x1, y1: Coordinates of the second point [batch_size]
            num_samples: Number of sample points to use for integration
            env_ids: Optional environment indices to calculate volumes for
                     Use ... (ellipsis) to calculate volumes for all environments

        Returns:
            Volume under the soil surface between the two points [n_envs, batch_size]
            where n_envs is the number of environments specified by env_ids
        """
        batch_size = x0.shape[0]

        # Handle case where x0 is a tensor containing multiple points
        if batch_size == 1 and x0.numel() > 1:
            # Reshape inputs to regular batch format
            x0_reshaped = x0.reshape(-1)
            y0_reshaped = y0.reshape(-1)
            x1_reshaped = x1.reshape(-1)
            y1_reshaped = y1.reshape(-1)

            # Call this method recursively with the reshaped inputs
            volumes_flat = self.integrate(
                x0_reshaped, y0_reshaped, x1_reshaped, y1_reshaped, num_samples, env_ids
            )

            # Reshape the results back and take the mean for each environment
            volumes = volumes_flat.mean(dim=1, keepdim=True)

            return volumes

        # Determine number of environments
        if env_ids is ...:
            n_envs = self.n_envs
        else:
            n_envs = len(env_ids) if hasattr(env_ids, "__len__") else 1

        # Generate integration grid points for all batch items at once - optimized
        # We only need one parameter (t) for linear interpolation along the line
        t = torch.linspace(0, 1, num_samples, device=self.device)

        # Create grid for sampling points along the line - more efficient expansion
        t_grid = t.view(-1, 1).expand(num_samples, num_samples)

        # Process each batch item
        all_volumes = []

        for b in range(batch_size):
            # Create a quadrilateral grid of points using bilinear interpolation
            # For x points, interpolate between x0 and x1
            x_points = (1 - t_grid) * x0[b] + t_grid * x1[b]

            # For y points, interpolate between y0 and y1
            # Fix the incorrect formula that was using y0 for all corners
            y_points = (1 - t_grid) * y0[b] + t_grid * y1[b]

            # Flatten the points for efficient querying
            x_flat = x_points.reshape(-1)
            y_flat = y_points.reshape(-1)

            # Get heights in a single operation, passing env_ids
            heights = self.get_height(
                x_flat, y_flat, env_ids
            )  # [n_envs, num_samples*num_samples]

            # Reshape heights back to grid
            heights_grid = heights.reshape(n_envs, num_samples, num_samples)

            # Calculate area of the quadrilateral - optimized
            dX = x1[b] - x0[b]
            dY = y1[b] - y0[b]
            area = dX**2 + dY**2  # Simplified: sqrt(dX^2 + dY^2)^2 = dX^2 + dY^2

            # Compute average height and volume
            avg_height = heights_grid.mean(dim=(1, 2))  # [n_envs]
            volume = avg_height * area

            all_volumes.append(volume)

        # Stack all batch volumes
        volumes = torch.stack(all_volumes, dim=1)  # [n_envs, batch_size]

        return volumes

    def make_trench(self, midpoint, depth, bottom_length, slope_tan):
        """Create a predefined trench shape in the terrain.
        Vectorized implementation for all environments simultaneously.

        Args:
            midpoint: X-coordinate of the middle of the trench bottom
            depth: Depth of the trench
            bottom_length: Length of the flat bottom of the trench
            slope_tan: Tangent of the slope angle (rise/run)
        """
        # Find grid indices corresponding to trench parameters
        midpoint_idx_x = int(
            (midpoint - self.x_min) / (self.x_max - self.x_min) * (self.grid_size_x - 1)
        )
        bottom_points = int(
            bottom_length / ((self.x_max - self.x_min) / (self.grid_size_x - 1))
        )

        # Calculate index ranges for trench sections
        min_idx = 0
        max_idx_x = self.grid_size_x - 1

        bottom_start_idx_x = max(midpoint_idx_x - bottom_points // 2, min_idx)
        bottom_end_idx_x = min(midpoint_idx_x + bottom_points // 2, max_idx_x)

        cell_size_x = (self.x_max - self.x_min) / (self.grid_size_x - 1)
        slope_points = int((depth / slope_tan) / cell_size_x)

        slope_start_idx_x = max(bottom_start_idx_x - slope_points, min_idx)
        slope_end_idx_x = min(bottom_end_idx_x + slope_points, max_idx_x)

        # Create mask tensors for vectorized operations
        x_indices = torch.arange(self.grid_size_x, device=self.device)

        # Create masks for different regions
        left_slope_mask = (x_indices >= slope_start_idx_x) & (
            x_indices < bottom_start_idx_x
        )
        bottom_mask = (x_indices >= bottom_start_idx_x) & (x_indices < bottom_end_idx_x)
        right_slope_mask = (x_indices >= bottom_end_idx_x) & (
            x_indices < slope_end_idx_x
        )

        # Reset only the trench area, preserving the rest of the terrain
        # First, create a mask for the entire trench area
        trench_area_mask = torch.zeros(
            (self.grid_size_x,), dtype=torch.bool, device=self.device
        )
        trench_area_mask[slope_start_idx_x:slope_end_idx_x] = True

        # Apply shapes to all environments at once
        for j in range(self.grid_size_y):
            # Create a temporary tensor for the trench heights in this row
            trench_heights = torch.zeros((self.grid_size_x,), device=self.device)

            # Left slope
            if left_slope_mask.any():
                slope_idxs = torch.arange(
                    bottom_start_idx_x - slope_start_idx_x,
                    device=self.device,
                    dtype=torch.float32,
                )
                slope_heights = -slope_tan * slope_idxs * cell_size_x
                trench_heights[left_slope_mask] = slope_heights

            # Flat bottom
            if bottom_mask.any():
                trench_heights[bottom_mask] = -depth

            # Right slope
            if right_slope_mask.any():
                slope_idxs = torch.arange(
                    slope_end_idx_x - bottom_end_idx_x,
                    device=self.device,
                    dtype=torch.float32,
                )
                slope_heights = -depth + slope_tan * slope_idxs * cell_size_x
                trench_heights[right_slope_mask] = slope_heights

            # Apply the trench heights only to the trench area, preserving the rest
            # Expand trench_heights for all environments
            trench_heights_expanded = trench_heights.unsqueeze(0).expand(
                self.n_envs, -1
            )

            # Update only the trench area for this row
            self.height_field[:, trench_area_mask, j] = trench_heights_expanded[
                :, trench_area_mask
            ]

        return self.height_field

    def get_visualization_data(self):
        """Get data for visualization of the height field.

        Returns:
            Tuple of (x_positions, heights) where heights is [n_envs, n_points]
        """
        # Return the first row of the height field (y=0) for visualization
        return self.x_grid, self.height_field[:, :, self.grid_size_y // 2]
