# this loads necessary paths for the isaac sim python
ISAAC_SIM="/home/<USER>/Isaaclab/_isaac_sim"
ISAACLAB_PATH="/home/<USER>/Isaaclab"
CARB_APP_PATH="/home/<USER>/Isaaclab/_isaac_sim/kit"
ISAAC_PATH="/home/<USER>/Isaaclab/_isaac_sim"
EXP_PATH="/home/<USER>/Isaaclab/_isaac_sim/apps"
PYTHONPATH="/home/<USER>/Isaaclab/_isaac_sim/../../../${PYTHONPATH}:/home/<USER>/Isaaclab/_isaac_sim/kit/python/lib/python3.10/site-packages:/home/<USER>/Isaaclab/_isaac_sim/python_packages:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.isaac.kit:/home/<USER>/Isaaclab/_isaac_sim/kit/kernel/py:/home/<USER>/Isaaclab/_isaac_sim/kit/plugins/bindings-python:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.isaac.lula/pip_prebundle:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.exporter.urdf/pip_prebundle:/home/<USER>/Isaaclab/_isaac_sim/extscache/omni.kit.pip_archive-0.0.0+10a4b5c0.lx64.cp310/pip_prebundle:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.isaac.core_archive/pip_prebundle:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.isaac.ml_archive/pip_prebundle:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.pip.compute/pip_prebundle:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.pip.cloud/pip_prebundle"
LD_LIBRARY_PATH="/home/<USER>/Isaaclab/_isaac_sim/../../../${LD_LIBRARY_PATH}:/home/<USER>/Isaaclab/_isaac_sim/.:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.usd.schema.isaac/plugins/IsaacSensorSchema/lib:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.usd.schema.isaac/plugins/RangeSensorSchema/lib:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.isaac.lula/pip_prebundle:/home/<USER>/Isaaclab/_isaac_sim/exts/omni.exporter.urdf/pip_prebundle:/home/<USER>/Isaaclab/_isaac_sim/kit:/home/<USER>/Isaaclab/_isaac_sim/kit/kernel/plugins:/home/<USER>/Isaaclab/_isaac_sim/kit/libs/iray:/home/<USER>/Isaaclab/_isaac_sim/kit/plugins:/home/<USER>/Isaaclab/_isaac_sim/kit/plugins/bindings-python:/home/<USER>/Isaaclab/_isaac_sim/kit/plugins/carb_gfx:/home/<USER>/Isaaclab/_isaac_sim/kit/plugins/rtx:/home/<USER>/Isaaclab/_isaac_sim/kit/plugins/gpu.foundation"
RESOURCE_NAME="IsaacSim"
