# Cluster Operations Guide

This guide focuses on cluster-specific operations for deploying moleworks_ext training jobs. For general Docker setup and local development, see the main [Docker README](../README.md).

## Prerequisites

1. **SSH access** to your cluster
2. **Docker and Apptainer** installed locally
3. **Environment file**: Copy and configure the cluster environment:
   ```bash
   cp .env.cluster.template .env.cluster
   ```
   Edit `.env.cluster` with your cluster-specific settings:
   - `CLUSTER_USER`: Your cluster username
   - `CLUSTER_LOGIN`: SSH login string (e.g., `<EMAIL>`)
   - `CLUSTER_ISAACLAB_DIR`: Base directory for experiments on cluster
   - `CLUSTER_SIF_PATH`: Directory for Singularity images
   - `CLUSTER_JOB_SCHEDULER`: SLURM or PBS
   - `CLUSTER_PYTHON_EXECUTABLE`: Script to run (e.g., `scripts/rl/rsl_rl/train.py`)

## Quick Start

```bash
# 1. Build container locally (if not already done)
cd ../
./container.sh -p ext build

# 2. Push container to cluster
cd cluster
./cluster_interface.sh push moleworks_ext

# 3. Submit training job
./cluster_interface.sh job moleworks_ext --task Isaac-m545-digging --num_envs 64000

# 4. Sync logs back to local machine
./sync_experiments.sh --remove ~/experiments/logs
```

## Detailed Operations

### Pushing Container to Cluster

The push operation converts your Docker image to Singularity format and uploads it:

```bash
./cluster_interface.sh push <profile>

# Example
./cluster_interface.sh push moleworks_ext
```

## Submitting a Job to the Cluster

### Single GPU Training (Original Method)

1. **Set the script** in `CLUSTER_PYTHON_EXECUTABLE` within `.env.cluster`.
2. **Submit a job**:

    ```bash
    cd docker/cluster
    ./cluster_interface.sh job <profile> <command>
    # Optionally, to check for large files after sync:
    # ./cluster_interface.sh -c job <profile> <command>
    ```

For example, to run the `rsl_rl/train.py` script:
**Note**: The image must be named `isaac-lab-<profile>` (e.g., `isaac-lab-moleworks_ext`).

### Submitting Jobs

Submit jobs with custom arguments:

```bash
./cluster_interface.sh job <profile> [arguments]

# Examples
./cluster_interface.sh job moleworks_ext --task Isaac-m545-digging --num_envs 64000
./cluster_interface.sh job moleworks_ext --task Isaac-m545-boulder --headless

# Check for large files after sync (adds validation step)
./cluster_interface.sh -c job moleworks_ext --task Isaac-m545-digging
```

### External Codebase Mounting

The cluster system supports the unified mount configuration:

```bash
# Configure mounts locally before pushing
cd ../
./container.sh mount-setup

# Mounts are automatically synced with the container
cd cluster
./cluster_interface.sh push moleworks_ext
```

#### Mount Modes

1. **Sync Mode** (default): Syncs codebase from local to cluster
2. **Mount-Only Mode**: Uses existing codebase on cluster without syncing

```bash
# Configure mount-only mode
cd ../
./container.sh mount-enable isaaclab
./container.sh mount-set-sync isaaclab off
./container.sh mount-set-cluster isaaclab /cluster/home/<USER>/isaaclab
```

### Synchronizing Logs

Sync experiment logs from cluster to local machine:

```bash
# Basic sync
./sync_experiments.sh

# Sync to specific folder
./sync_experiments.sh ~/my-experiments

# Sync and remove remote logs
./sync_experiments.sh --remove ~/experiments/logs
```

## Job Management

### Check Job Status

```bash
# SLURM
ssh $CLUSTER_LOGIN "squeue -u $USER"

# PBS
ssh $CLUSTER_LOGIN "qstat -u $USER"
```

### Cancel Jobs

```bash
# SLURM
ssh $CLUSTER_LOGIN "scancel <job_id>"

# PBS
ssh $CLUSTER_LOGIN "qdel <job_id>"
```

### View Job Output

Job outputs are stored in the exports directory:
```bash
ssh $CLUSTER_LOGIN "ls -la $CLUSTER_ISAACLAB_DIR/exports/"
ssh $CLUSTER_LOGIN "tail -f $CLUSTER_ISAACLAB_DIR/exports/<job_output_file>"
```

## Environment Variables

Key variables in `.env.cluster`:

| Variable                      | Description           | Example                                  |
| ----------------------------- | --------------------- | ---------------------------------------- |
| `CLUSTER_USER`                | Your cluster username | `jsmith`                                 |
| `CLUSTER_LOGIN`               | SSH login string      | `<EMAIL>`                   |
| `CLUSTER_ISAACLAB_DIR`        | Experiment directory  | `/cluster/scratch/$USER/isaaclab`        |
| `CLUSTER_SIF_PATH`            | Singularity images    | `/cluster/home/<USER>/.singularity`       |
| `CLUSTER_JOB_SCHEDULER`       | Job system            | `SLURM` or `PBS`                         |
| `CLUSTER_PYTHON_EXECUTABLE`   | Script to run         | `scripts/rl/rsl_rl/train.py`             |
| `CLUSTER_ISAAC_SIM_CACHE_DIR` | Isaac Sim cache       | `/cluster/scratch/$USER/isaac-sim-cache` |
| `REMOVE_CODE_COPY_AFTER_JOB`  | Cleanup after job     | `true` or `false`                        |

## Troubleshooting

### Container Push Fails

```bash
# Check Docker image exists
docker images | grep isaac-lab-moleworks_ext

# Verify SSH connection
ssh $CLUSTER_LOGIN "echo 'Connection successful'"

# Check available space
ssh $CLUSTER_LOGIN "df -h $CLUSTER_SIF_PATH"
```

### Docker Daemon Connection Issues (Student PCs)

If you encounter this error on student computers:

```
FATAL: While performing build: conveyor failed to get: while converting reference: loading image from docker engine: Cannot connect to the Docker daemon at unix:///var/run/docker.sock. Is the docker daemon running?
```

Start the user Docker service and set the correct socket path:

```bash
# Start Docker service for the current user
systemctl --user start docker

# Export the correct Docker socket path
export DOCKER_HOST=unix:///run/user/$(id -u)/docker.sock
```

### Job Submission Issues

```bash
# Verify Singularity image on cluster
ssh $CLUSTER_LOGIN "ls -la $CLUSTER_SIF_PATH/*.tar"

# Check job script was created
ssh $CLUSTER_LOGIN "ls -la $CLUSTER_ISAACLAB_DIR/*.sh"

# View error logs
ssh $CLUSTER_LOGIN "cat $CLUSTER_ISAACLAB_DIR/exports/*.err"
```

### Performance Tips

1. **Use appropriate `--num_envs`**: Balance between GPU memory and parallelism
2. **Enable headless mode**: Add `--headless` for better performance
3. **Monitor GPU usage**: Check with `nvidia-smi` during training
4. **Use local scratch**: Configure `CLUSTER_ISAAC_SIM_CACHE_DIR` to use fast local storage

## Advanced Usage

### Custom Job Scripts

For complex workflows, create custom submission scripts:

```bash
# Edit submit_job_slurm.sh or submit_job_pbs.sh
# Modify resource requirements, modules, etc.
```

### Multi-GPU Training

Configure multi-GPU jobs in the submission scripts:
- SLURM: Modify `#SBATCH --gres=gpu:X`
- PBS: Modify `#PBS -l select=1:ncpus=X:ngpus=Y`

### Batch Job Submission

Submit multiple experiments:

```bash
for task in Isaac-m545-digging Isaac-m545-boulder; do
    ./cluster_interface.sh job moleworks_ext --task $task --num_envs 32000
done
```